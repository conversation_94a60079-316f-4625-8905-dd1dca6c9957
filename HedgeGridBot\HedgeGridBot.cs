using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using cAlgo.API;
using cAlgo.API.Collections;
using cAlgo.API.Indicators;
using cAlgo.API.Internals;

namespace cAlgo.Robots
{
    public enum GridDirection
    {
        StartFromHigh,
        StartFromLow
    }

    [Robot(AccessRights = AccessRights.FullAccess, TimeZone = TimeZones.IndiaStandardTime)]
    public class HedgeGridBot : Robot
    {
        // Input Parameters
        [Parameter("Range High", DefaultValue = 1.1000)]
        public double RangeHigh { get; set; }

        [Parameter("Range Low", DefaultValue = 1.0900)]
        public double RangeLow { get; set; }

        [Parameter("Grid Level Distance (Pips)", DefaultValue = 10, MinValue = 1)]
        public double GridLevelDistancePips { get; set; }

        [Parameter("Volume (Lots)", DefaultValue = 0.01, MinValue = 0.01)]
        public double Volume { get; set; }

        [Parameter("Max Grid Level Cross", DefaultValue = 3, MinValue = 1)]
        public int MaxGridLevelCross { get; set; }


        [Parameter("Ignore Grid Levels ")] public string IgnoreGridLevels { get; set; }

        [Parameter("Stop Pips Buffer", DefaultValue = 5, MinValue = 0)]
        public double StopPipsBuffer { get; set; }

        [Parameter("Max Runtime Bars", DefaultValue = 100, MinValue = 1)]
        public int MaxRuntimeBars { get; set; }

        [Parameter("Manage Existing Positions", DefaultValue = false)]
        public bool ManageExistingPositions { get; set; }

        [Parameter("Grid Direction", DefaultValue = GridDirection.StartFromHigh)]
        public GridDirection GridDirection { get; set; }

        // Private fields
        private List<double> gridLevels;

        private readonly HashSet<int> _ignoredGridIndices = new HashSet<int>();


        private double gridSpacing;
        private int startBarIndex;
        private bool isRuntimeExpired;
        private string GRID_LINE_PREFIX;
        private string BUY_LABEL_PREFIX;
        private string SELL_LABEL_PREFIX;
        private bool _pauseBuyFlag, _pauseSellFlag;
        private int _redrawPeriod = 5;
        private int _redrawCounter = 0;
        private bool _startTradingFlag = false;

        protected override void OnStart()
        {
            // Debugger.Launch();
            // Validate parameters
            if (RangeHigh <= RangeLow)
            {
                Print("Error: Range High must be greater than Range Low");
                Stop();
                return;
            }

            // Initialize unique prefixes with symbol and timeframe
            string uniqueId = $"{SymbolName}_{TimeFrame}";
            GRID_LINE_PREFIX = $"GridLine_{uniqueId}_";
            BUY_LABEL_PREFIX = $"GridBuy_{uniqueId}_";
            SELL_LABEL_PREFIX = $"GridSell_{uniqueId}_";

            // Initialize collections
            gridLevels = new List<double>();

            // Initialize runtime tracking
            startBarIndex = Bars.Count - 1;
            isRuntimeExpired = false;

            // Calculate grid levels and spacing
            CalculateGridLevels();

            // Draw grid lines on chart
            ParseIgnoreGridLevels();
            DrawGridLines();

            AddUi();

            // Subscribe to position events

            Print($"HedgeGridBot started with {gridLevels.Count} grid levels between {RangeLow} and {RangeHigh}");
            Print($"Grid spacing: {GridLevelDistancePips} pips ({gridSpacing:F5} price units)");
            Print($"Bot will automatically stop after {MaxRuntimeBars} bars from start (Current bar: {startBarIndex})");
        }


        private void ParseIgnoreGridLevels()
        {
            _ignoredGridIndices.Clear();

            if (string.IsNullOrWhiteSpace(IgnoreGridLevels))
            {
                Print("No grid levels to ignore - IgnoreGridLevels parameter is empty");
                return;
            }

            try
            {
                string[] parts = IgnoreGridLevels.Split(',');
                foreach (string part in parts)
                {
                    string trimmedPart = part.Trim();
                    if (string.IsNullOrEmpty(trimmedPart))
                    {
                        Print($"Skipping empty grid level entry in ignore list");
                        continue;
                    }

                    if (int.TryParse(trimmedPart, out int gridIndex))
                    {
                        // Validate that the grid index is within valid range
                        if (gridIndex > 0 && gridIndex <= gridLevels.Count)
                        {
                            _ignoredGridIndices.Add(gridIndex);
                            Print($"Added grid level {gridIndex} to ignore list");
                        }
                        else
                        {
                            var errorMessage =
                                $"Grid level index {gridIndex} is out of range, Valid Range: (1–{gridLevels.Count}).";
                            Print($"ERROR: {errorMessage}");
                            MessageBox.Show(errorMessage, "Invalid Grid Level", MessageBoxButton.OK,
                                MessageBoxImage.Error);
                            Stop();
                            return;
                        }
                    }
                    else
                    {
                        string errorMessage =
                            $"Could not parse '{trimmedPart}' as a grid level index. Expected integer values separated by commas.";
                        Print($"ERROR: {errorMessage}");
                        MessageBox.Show(errorMessage, "Invalid Grid Level Format", MessageBoxButton.OK,
                            MessageBoxImage.Error);
                        Stop();
                        return;
                    }
                }

                if (_ignoredGridIndices.Count > 0)
                {
                    Print($"Ignoring grid levels at indices: {string.Join(", ", _ignoredGridIndices)}");
                }
                else
                {
                    Print("No valid grid levels found in ignore list");
                }
            }
            catch (Exception ex)
            {
                string errorMessage = $"Error parsing ignore grid levels: {ex.Message}";
                Print($"EXCEPTION: {errorMessage}");
                MessageBox.Show(errorMessage, "Parsing Error", MessageBoxButton.OK, MessageBoxImage.Error);
                Stop();
            }
        }

        private void AddUi()
        {
            var stackPanel = new StackPanel()
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Left,
                VerticalAlignment = VerticalAlignment.Bottom,
            };

            var stopBuyCheckBox = new CheckBox()
            {
                Text = "Stop Buy",
                Margin = new Thickness(10),
                IsChecked = false,
                FontSize = 18
            };

            var stopSellCheckBox = new CheckBox()
            {
                Text = "Stop Sell",
                Margin = new Thickness(10),
                IsChecked = false,
                FontSize = 18
            };


            var startTradingCheckbox = new CheckBox()
            {
                Text = "startTrading",
                Margin = new Thickness(10),
                IsChecked = false,
                FontSize = 18
            };

            stopBuyCheckBox.Click += OnStopBuyCheckBoxOnChecked;
            stopSellCheckBox.Click += OnStopSellCheckBoxOnChecked;
            startTradingCheckbox.Click += args =>
            {
                _startTradingFlag = (bool)args.CheckBox.IsChecked;
                Print($"Current Status For Trading: {_startTradingFlag}");
            };

            // Create nested stackpanel for grid color buttons
            var gridColorStackPanel = new StackPanel()
            {
                Orientation = Orientation.Vertical,
                Margin = new Thickness(10, 0, 0, 0)
            };

            var onlyBuyButton = new Button()
            {
                Text = "Only Buy",
                Margin = new Thickness(2),
                FontSize = 12,
                BackgroundColor = Color.Green,
                ForegroundColor = Color.White
            };

            var onlySellButton = new Button()
            {
                Text = "Only Sell",
                Margin = new Thickness(2),
                FontSize = 12,
                BackgroundColor = Color.Red,
                ForegroundColor = Color.White
            };

            var defaultButton = new Button()
            {
                Text = "Default",
                Margin = new Thickness(2),
                FontSize = 12,
                BackgroundColor = Color.Yellow,
                ForegroundColor = Color.Black
            };

            // Add click handlers
            onlyBuyButton.Click += OnOnlyBuyButtonClick;
            onlySellButton.Click += OnOnlySellButtonClick;
            defaultButton.Click += OnDefaultButtonClick;

            // Add buttons to nested stackpanel
            gridColorStackPanel.AddChild(onlyBuyButton);
            gridColorStackPanel.AddChild(onlySellButton);
            gridColorStackPanel.AddChild(defaultButton);

            // Add all controls to main stackpanel
            stackPanel.AddChild(stopBuyCheckBox);
            stackPanel.AddChild(stopSellCheckBox);
            stackPanel.AddChild(startTradingCheckbox);
            stackPanel.AddChild(gridColorStackPanel);

            Chart.AddControl(stackPanel);
        }

        private void OnStopSellCheckBoxOnChecked(CheckBoxEventArgs args)
        {
            _pauseSellFlag = (bool)args.CheckBox.IsChecked;
            Print($"Current Status For Sells: {!_pauseSellFlag}");
        }

        private void OnStopBuyCheckBoxOnChecked(CheckBoxEventArgs args)
        {
            _pauseBuyFlag = (bool)args.CheckBox.IsChecked;
            Print($"Current Status For Buys: {!_pauseBuyFlag}");
        }

        private void OnOnlyBuyButtonClick(ButtonClickEventArgs args)
        {
            ChangeGridLineColors(Color.Green);
            Print("Grid lines set to Only Buy mode (Green)");
        }

        private void OnOnlySellButtonClick(ButtonClickEventArgs args)
        {
            ChangeGridLineColors(Color.Red);
            Print("Grid lines set to Only Sell mode (Red)");
        }

        private void OnDefaultButtonClick(ButtonClickEventArgs args)
        {
            ChangeGridLineColors(Color.Yellow);
            Print("Grid lines set to Default mode (Yellow)");
        }

        private void ChangeGridLineColors(Color newColor)
        {
            var selectedObjects = Chart.SelectedObjects;

            if (selectedObjects.Count == 0)
            {
                Print("No grid lines selected for color change - showing message to user");
                MessageBox.Show("Please select grid lines first to change their color.", "No Selection",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            int colorChangedCount = 0;
            foreach (var obj in selectedObjects)
            {
                if (obj is ChartHorizontalLine horizontalLine &&
                    horizontalLine.Name.StartsWith(GRID_LINE_PREFIX))
                {
                    horizontalLine.Color = newColor;
                    colorChangedCount++;
                }
            }

            Print($"Changed color of {colorChangedCount} grid lines to {newColor}");
        }

        private void CalculateGridLevels()
        {
            // Convert pips to price distance
            gridSpacing = GridLevelDistancePips * Symbol.PipSize;

            if (GridDirection == GridDirection.StartFromHigh)
            {
                // Start from Range High and work down to Range Low
                double currentLevel = RangeHigh;

                while (currentLevel >= RangeLow)
                {
                    gridLevels.Add(currentLevel);
                    currentLevel -= gridSpacing;
                }
            }
            else // StartFromLow
            {
                // Start from Range Low and work up to Range High
                double currentLevel = RangeLow;

                while (currentLevel <= RangeHigh)
                {
                    gridLevels.Add(currentLevel);
                    currentLevel += gridSpacing;
                }
            }

            // Always sort grid levels consistently (high to low) regardless of creation direction
            gridLevels.Sort((a, b) => b.CompareTo(a));

            Print($"Created {gridLevels.Count} grid levels with {GridLevelDistancePips} pips spacing");

            // Manage existing positions if enabled
            if (ManageExistingPositions)
            {
                ManageExistingGridPositions();
            }
        }

        private void ManageExistingGridPositions()
        {
            var existingPositions = Positions.Where(p => p.Label.Contains(BUY_LABEL_PREFIX) || p.Label.Contains(SELL_LABEL_PREFIX) ).ToList();

            if (existingPositions.Count == 0)
            {
                Print("No existing positions found to manage");
                return;
            }

            Print($"Managing {existingPositions.Count} existing positions based on new grid");

            foreach (var position in existingPositions)
            {
                double nearestGridLevel = FindNearestGridLevel(position.EntryPrice);
                int gridIndex = gridLevels.IndexOf(nearestGridLevel);

                if (gridIndex >= 0)
                {
                    // Calculate new stop loss based on grid
                    double newStopLoss = CalculateStopLoss(position.TradeType, gridIndex);

                    if (newStopLoss <= 0)
                    {
                        Print($"Invalid stop loss calculated for position {position.Label} - skipping update");
                        continue;
                    }

                    // Only update if the new stop loss is different
                    if (position.StopLoss == null || Math.Abs(position.StopLoss.Value - newStopLoss) > Symbol.TickSize)
                    {
                        Print(
                            $"Attempting to update SL for position {position.Label} from {position.StopLoss?.ToString("F5") ?? "NONE"} to {newStopLoss:F5}");

                        var result = position.ModifyStopLossPrice(newStopLoss);
                        if (result.IsSuccessful)
                        {
                            Print($"✅ Updated SL for position {position.Label} | Entry: {position.EntryPrice:F5} | " +
                                  $"Nearest Grid: {nearestGridLevel:F5} | New SL: {newStopLoss:F5}");
                        }
                        else
                        {
                            Print($"❌ Failed to update SL for position {position.Label}: {result.Error}");
                        }
                    }
                    else
                    {
                        Print(
                            $"SL for position {position.Label} already at correct level ({newStopLoss:F5}) - no update needed");
                    }
                }
                else
                {
                    Print(
                        $"Could not find grid index for position {position.Label} at entry price {position.EntryPrice:F5} (nearest grid: {nearestGridLevel:F5})");
                }
            }
        }

        private double FindNearestGridLevel(double price)
        {
            if (gridLevels.Count == 0)
                return price;

            double nearestLevel = gridLevels[0];
            double minDistance = Math.Abs(price - nearestLevel);

            foreach (double level in gridLevels)
            {
                double distance = Math.Abs(price - level);
                if (distance < minDistance)
                {
                    minDistance = distance;
                    nearestLevel = level;
                }
            }

            return nearestLevel;
        }

        private void DrawGridLines()
        {
            for (int i = 0; i < gridLevels.Count; i++)
            {
                string lineName = GRID_LINE_PREFIX + i;
                string textName = GRID_LINE_PREFIX + "text_" + i;
                var line = Chart.DrawHorizontalLine(lineName, gridLevels[i],
                    i == 0 || i == gridLevels.Count - 1 ? Color.White : Color.Yellow);
                line.Thickness = 3;
                line.LineStyle = LineStyle.DotsRare;
                line.IsInteractive = true;
                line.IsLocked = true;

                bool isRangeLow = i == 0;
                bool isRangeHigh = i == gridLevels.Count - 1;
                bool isBoundary = isRangeLow || isRangeHigh;
                bool isIgnored = _ignoredGridIndices.Contains(i);

                string label = isRangeLow ? "Range Low" :
                    isRangeHigh ? "Range High" :
                    $"Level: {i}";
                string finalLabel = label + (isIgnored ? " IGNORED" : "");

                var text = Chart.DrawText(textName, finalLabel, Chart.LastVisibleBarIndex + _redrawPeriod * 4,
                    gridLevels[i],
                    isBoundary ? Color.Red : Color.White);
                text.HorizontalAlignment = HorizontalAlignment.Center;
                text.VerticalAlignment = VerticalAlignment.Top;
            }
        }

        protected override void OnBar()
        {
            // Check if runtime has expired (only on new bars)
            if (!isRuntimeExpired)
            {
                CheckRuntimeExpiration();

                // _redrawCounter++;
                // if (_redrawCounter >= _redrawPeriod)
                // {
                //     CLearGridChartObjects();
                //     DrawGridLines();
                //     _redrawCounter = 0;
                // }
            }
        }

        protected override void OnTick()
        {
            if (!_startTradingFlag)
            {
                // Print($"Trading is disabled via UI flag - skipping tick");
                return;
            }

            // Only continue trading if runtime hasn't expired
            if (!isRuntimeExpired)
            {
                // Check if price is outside the range first
                if (CheckPriceOutOfRange())
                {
                    Print("Exiting OnTick early - price is out of range");
                    return; // Exit early if price is out of range
                }

                CheckForGridCrosses();
            }
            else
            {
                Print("Runtime has expired - skipping tick processing");
            }
        }

        private bool CheckPriceOutOfRange()
        {
            double currentPrice = Bars.LastBar.Close;

            // Check if price is above range high or below range low
            if (currentPrice > RangeHigh || currentPrice < RangeLow)
            {
                Print($"⚠️ RANGE BREACH: Price {currentPrice:F5} is outside range [{RangeLow:F5} - {RangeHigh:F5}]");
                Print("Stopping bot and closing all positions...");

                // Close all positions
                CloseAllGridPositions();

                // Stop the bot
                Stop();

                return true; // Price is out of range
            }

            // Uncomment for very detailed debugging (will be very verbose)
            // Print($"Price {currentPrice:F5} is within range [{RangeLow:F5} - {RangeHigh:F5}]");
            return false; // Price is within range
        }

        private void CheckRuntimeExpiration()
        {
            int currentBarIndex = Bars.Count - 1;
            int barsElapsed = currentBarIndex - startBarIndex;

            if (barsElapsed >= MaxRuntimeBars)
            {
                isRuntimeExpired = true;
                Print($"Runtime expired after {barsElapsed} bars. Closing all positions and stopping bot...");

                CloseAllGridPositions();
                Stop();
            }
        }

        private void CloseAllGridPositions()
        {
            var gridPositions = Positions.Where(p => p.SymbolName == SymbolName &&
                                                     (p.Label.StartsWith(BUY_LABEL_PREFIX) ||
                                                      p.Label.StartsWith(SELL_LABEL_PREFIX))).ToList();

            Print($"Closing {gridPositions.Count} grid positions...");

            int successCount = 0;
            int failCount = 0;

            foreach (var position in gridPositions)
            {
                var result = ClosePosition(position);
                if (result.IsSuccessful)
                {
                    successCount++;
                    Print($"✅ Closed position {position.Label} | P&L: {position.NetProfit:F2}");
                }
                else
                {
                    failCount++;
                    Print($"❌ Failed to close position {position.Label}: {result.Error}");
                }
            }

            Print($"Position closure summary: {successCount} successful, {failCount} failed");
        }

        private void CheckForGridCrosses()
        {
            double barHigh = Bars.LastBar.High;
            double barLow = Bars.LastBar.Low;

            // Skip first (index 0) and last (index gridLevels.Count-1) elements
            for (int i = 1; i < gridLevels.Count - 1; i++)
            {
                double gridLevel = gridLevels[i];

                // Check if the current bar's range (high to low) crossed through the grid level
                bool hasCrossed = barLow <= gridLevel && barHigh >= gridLevel;

                if (hasCrossed)
                {
                    if (_ignoredGridIndices.Contains(i))
                    {
                        Print($"Ignoring grid level {i} because it is in the ignore list. Grid level: {gridLevel:F5}");
                        continue;
                    }

                    Print($"Price crossed grid level {i} at {gridLevel:F5} (Bar range: {barLow:F5} - {barHigh:F5})");
                    OpenHedgePositions(gridLevel);
                }
                else
                {
                    // Uncomment for very detailed debugging (will be very verbose)
                    // Print($"No crossing detected for grid level {i} at {gridLevel:F5} (Bar range: {barLow:F5} - {barHigh:F5})");
                }
            }
        }

        private int GetGridIndex(double gridLevel)
        {
            for (int i = 0; i < gridLevels.Count; i++)
            {
                if (Math.Abs(gridLevels[i] - gridLevel) < Symbol.TickSize)
                    return i;
            }

            Print($"WARNING: Could not find grid index for level {gridLevel:F5}");
            return -1;
        }

        private void OpenHedgePositions(double gridLevel)
        {
            int gridIndex = GetGridIndex(gridLevel);

            if (gridIndex == -1)
            {
                Print($"ERROR: Could not find grid index for level {gridLevel:F5} - throwing exception");
                throw new Exception($"Could not find grid index for level {gridLevel:F5}");
            }

            Print($"Processing grid crossing at level {gridIndex} ({gridLevel:F5})");

            // Check grid line color to determine trading mode
            string lineName = GRID_LINE_PREFIX + gridIndex;
            var gridLine = Chart.FindObject(lineName) as ChartHorizontalLine;

            if (gridLine == null)
            {
                Print($"ERROR: Grid line not found for level {gridIndex} - skipping trade execution");
                return;
            }

            Color lineColor = gridLine.Color;
            bool allowBuy = lineColor == Color.Green || lineColor == Color.Yellow;
            bool allowSell = lineColor == Color.Red || lineColor == Color.Yellow;

            Print($"Grid level {gridIndex} color: {lineColor} | Allow Buy: {allowBuy} | Allow Sell: {allowSell}");

            // Check if we should skip based on color restrictions
            if (!allowBuy && !allowSell)
            {
                Print($"SKIP: Grid level {gridIndex} color ({lineColor}) doesn't allow any trades");
                return;
            }

            // Check existing positions
            string buyLabel = BUY_LABEL_PREFIX + gridIndex;
            string sellLabel = SELL_LABEL_PREFIX + gridIndex;

            bool buyExists = Positions.Any(p => p.SymbolName == SymbolName && p.Label == buyLabel);
            bool sellExists = Positions.Any(p => p.SymbolName == SymbolName && p.Label == sellLabel);

            Print($"Position status at grid level {gridIndex}: Buy exists: {buyExists} | Sell exists: {sellExists}");

            // Skip if all allowed positions already exist
            if ((allowBuy && buyExists) && (allowSell && sellExists))
            {
                Print($"SKIP: All allowed positions already exist at grid level {gridIndex} ({gridLevel:F5})");
                return;
            }

            // Skip if only buy is allowed but buy position exists
            if (allowBuy && !allowSell && buyExists)
            {
                Print($"SKIP: Only buy allowed at grid level {gridIndex} but buy position already exists");
                return;
            }

            // Skip if only sell is allowed but sell position exists
            if (allowSell && !allowBuy && sellExists)
            {
                Print($"SKIP: Only sell allowed at grid level {gridIndex} but sell position already exists");
                return;
            }

            // Calculate prices once
            double volumeInUnits = Symbol.QuantityToVolumeInUnits(Volume);
            Print($"Volume calculation: {Volume} lots = {volumeInUnits} units");

            // Calculate take profit levels (grid is always sorted high to low)
            double buyTakeProfit = gridIndex > 0 ? gridLevels[gridIndex - 1] : 0; // Next higher level
            double sellTakeProfit =
                gridIndex < gridLevels.Count - 1 ? gridLevels[gridIndex + 1] : 0; // Next lower level

            Print($"Take profit levels: Buy TP: {buyTakeProfit:F5} | Sell TP: {sellTakeProfit:F5}");

            double buyStopLoss = CalculateStopLoss(TradeType.Buy, gridIndex);
            double sellStopLoss = CalculateStopLoss(TradeType.Sell, gridIndex);

            Print($"Stop loss levels: Buy SL: {buyStopLoss:F5} | Sell SL: {sellStopLoss:F5}");

            Print(
                $"Final levels: Buy TP: {buyTakeProfit:F5}, SL: {buyStopLoss:F5} | Sell TP: {sellTakeProfit:F5}, SL: {sellStopLoss:F5}");

            // Try to open Buy position (only if allowed by color and other conditions)
            if (!buyExists && buyTakeProfit > 0 && !_pauseBuyFlag && allowBuy)
            {
                Print($"Attempting to open BUY position at grid level {gridIndex}");
                OpenSinglePosition(TradeType.Buy, buyLabel, volumeInUnits, buyStopLoss, buyTakeProfit, gridIndex);
            }
            else if (!buyExists)
            {
                if (buyTakeProfit <= 0)
                    Print($"SKIP BUY: No valid take profit level at grid {gridIndex} (TP: {buyTakeProfit:F5})");
                else if (_pauseBuyFlag)
                    Print($"SKIP BUY: Buy trading is paused via UI at grid {gridIndex}");
                else if (!allowBuy)
                    Print($"SKIP BUY: Grid line color ({gridLine.Color}) doesn't allow buy trades at grid {gridIndex}");
            }
            else
            {
                Print($"SKIP BUY: Buy position already exists at grid level {gridIndex}");
            }

            // Try to open Sell position (only if allowed by color and other conditions)
            if (!sellExists && sellTakeProfit > 0 && !_pauseSellFlag && allowSell)
            {
                Print($"Attempting to open SELL position at grid level {gridIndex}");
                OpenSinglePosition(TradeType.Sell, sellLabel, volumeInUnits, sellStopLoss, sellTakeProfit, gridIndex);
            }
            else if (!sellExists)
            {
                if (sellTakeProfit <= 0)
                    Print($"SKIP SELL: No valid take profit level at grid {gridIndex} (TP: {sellTakeProfit:F5})");
                else if (_pauseSellFlag)
                    Print($"SKIP SELL: Sell trading is paused via UI at grid {gridIndex}");
                else if (!allowSell)
                    Print(
                        $"SKIP SELL: Grid line color ({gridLine.Color}) doesn't allow sell trades at grid {gridIndex}");
            }
            else
            {
                Print($"SKIP SELL: Sell position already exists at grid level {gridIndex}");
            }
        }

        private void OpenSinglePosition(TradeType tradeType, string label, double volumeInUnits,
            double stopLoss, double takeProfit, int gridIndex)
        {
            Print(
                $"Executing market order: {tradeType} | Symbol: {SymbolName} | Volume: {volumeInUnits} | Label: {label}");
            Print($"Planned SL: {stopLoss:F5} | Planned TP: {takeProfit:F5}");

            var result = ExecuteMarketOrder(tradeType, SymbolName, volumeInUnits, label,
                null, null, $"Grid {tradeType} at level {gridIndex}");

            if (result.IsSuccessful)
            {
                Print(
                    $"Market order successful - Position ID: {result.Position.Id} | Entry Price: {result.Position.EntryPrice:F5}");

                double entryPrice = result.Position.EntryPrice;

                // Set stop loss
                if (stopLoss > 0)
                {
                    var slResult = result.Position.ModifyStopLossPrice(stopLoss);
                    if (!slResult.IsSuccessful)
                        Print($"❌ Failed to set stop loss {stopLoss:F5}: {slResult.Error}");
                    else
                        Print($"✅ Stop loss set successfully: {stopLoss:F5}");
                }
                else
                {
                    Print($"⚠️ Skipping stop loss (invalid value: {stopLoss:F5})");
                }

                // Set take profit
                if (takeProfit > 0)
                {
                    var tpResult = result.Position.ModifyTakeProfitPrice(takeProfit);
                    if (!tpResult.IsSuccessful)
                        Print($"❌ Failed to set take profit {takeProfit:F5}: {tpResult.Error}");
                    else
                        Print($"✅ Take profit set successfully: {takeProfit:F5}");
                }
                else
                {
                    Print($"⚠️ Skipping take profit (invalid value: {takeProfit:F5})");
                }

                Print($"✅ SUCCESS: Opened {tradeType.ToString().ToUpper()} position at grid level {gridIndex} " +
                      $"(Entry: {entryPrice:F5}) | TP: {takeProfit:F5} | SL: {stopLoss:F5}");
            }
            else
            {
                Print(
                    $"❌ FAILED: Could not open {tradeType.ToString().ToUpper()} position at grid level {gridIndex}: {result.Error}");
            }
        }

        private double CalculateStopLoss(TradeType tradeType, int gridIndex)
        {
            if (gridLevels == null || gridIndex < 0 || gridIndex >= gridLevels.Count)
            {
                Print(
                    $"ERROR: Invalid grid parameters for SL calculation - gridIndex: {gridIndex}, gridLevels count: {gridLevels?.Count ?? 0}");
                return 0;
            }

            double currentGridLevel = gridLevels[gridIndex];
            Print($"Calculating {tradeType} stop loss for grid index {gridIndex} at level {currentGridLevel:F5}");

            if (tradeType == TradeType.Buy)
            {
                // For buy trades: SL should be MaxGridLevelCross levels below current level
                // Grid is always sorted high to low, so higher index = lower price
                int stopLossIndex = Math.Min(gridLevels.Count - 1, gridIndex + MaxGridLevelCross);
                double baseStopPrice = gridLevels[stopLossIndex];

                // Apply buffer: move SL further down by buffer amount
                double stopLoss = baseStopPrice - (StopPipsBuffer * Symbol.PipSize);

                Print(
                    $"Buy SL calculation: grid {gridIndex} -> SL grid {stopLossIndex} | Base: {baseStopPrice:F5} | Buffer: {StopPipsBuffer} pips | Final SL: {stopLoss:F5}");

                // Validate that SL is below current grid level
                if (stopLoss >= currentGridLevel)
                {
                    Print($"WARNING: Buy SL {stopLoss:F5} is not below current grid level {currentGridLevel:F5}");
                }

                return stopLoss;
            }
            else
            {
                // For sell trades: SL should be MaxGridLevelCross levels above current level
                // Grid is always sorted high to low, so lower index = higher price
                int stopLossIndex = Math.Max(0, gridIndex - MaxGridLevelCross);
                double baseStopPrice = gridLevels[stopLossIndex];

                // Apply buffer: move SL further up by buffer amount
                double stopLoss = baseStopPrice + (StopPipsBuffer * Symbol.PipSize);

                Print(
                    $"Sell SL calculation: grid {gridIndex} -> SL grid {stopLossIndex} | Base: {baseStopPrice:F5} | Buffer: {StopPipsBuffer} pips | Final SL: {stopLoss:F5}");

                // Validate that SL is above current grid level
                if (stopLoss <= currentGridLevel)
                {
                    Print($"WARNING: Sell SL {stopLoss:F5} is not above current grid level {currentGridLevel:F5}");
                }

                return stopLoss;
            }
        }


        protected override void OnException(Exception exception)
        {
            Print($"Exception: {exception.Message}");
            Print(exception.StackTrace);

            Stop();
        }

        protected override void OnError(Error error)
        {
            Print(error.ToString());
            Stop();
        }

        protected override void OnStop()
        {
            try
            {
                Print("OnStop called - cleaning up resources");

                // Clean up chart objects
                CLearGridChartObjects();

                if (RunningMode != RunningMode.RealTime)
                {
                    CloseAllGridPositions();
                }

                Print("HedgeGridBot stopped and chart objects cleaned up");
            }
            catch (Exception ex)
            {
                Print($"Error in OnStop: {ex.Message}");
            }
        }

        private void CLearGridChartObjects()
        {
            try
            {
                if (gridLevels == null || gridLevels.Count == 0)
                {
                    Print("No grid levels to clean up");
                    return;
                }

                Print($"Cleaning up {gridLevels.Count} grid chart objects");

                for (int i = 0; i < gridLevels.Count; i++)
                {
                    string lineName = GRID_LINE_PREFIX + i;
                    string textName = GRID_LINE_PREFIX + "text_" + i;

                    Chart.RemoveObject(lineName);
                    Chart.RemoveObject(textName);
                }

                Print("Grid chart objects cleanup completed");
            }
            catch (Exception ex)
            {
                Print($"Error cleaning grid chart objects: {ex.Message}");
            }
        }
    }
}